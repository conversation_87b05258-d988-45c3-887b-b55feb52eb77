<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Match</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <script>
	//document.addEventListener('DOMContentLoaded', function() {
    //console.log('DOM fully loaded and parsed');
		const matchId = "{{ match_id }}";
        const homeTeam = "{{ match.home_team.name }}";
        const awayTeam = "{{ match.away_team.name }}";
		const socket = new WebSocket(`ws://${window.location.hostname}:8001/ws/match/${matchId}/`);

		//const button = document.createElement('button');
		//button.className = 'collapsible';
		//button.innerHTML = 'Quarter ' + ' Player Stats';

		//const contentDiv = document.createElement('div');
		//contentDiv.className = 'collapsible-content';



		socket.onopen = () => {
			console.log("WebSocket connected");
			socket.send(JSON.stringify({ command: "start_simulation" }));
		};

        let matchStarted = false;

        socket.onmessage = function(e) {
            const data = JSON.parse(e.data);

            // Remove loading indicator if this is the first event
            if (!matchStarted) {
                const loadingDiv = document.querySelector('.loading');
                if (loadingDiv) {
                    loadingDiv.remove();
                    matchStarted = true;
                }
            }

            if (data.type === "match_event") {
                console.log("match_event received:", data.data);
                displayMatchEvent(data.data);
            }
            else if (data.type === "score_update") {
                console.log("Score update received:", data.data);
                updateScore(data.data);
                updateQuarter(data.data.quarter);
            }
            else if (data.type === "player_stats") {
                console.log("Player stats received:", data.data);
                updatePlayerStats(data.data);
            }
            else if (data.type === "position_update" && data.data) {
                console.log("Positions received:", data.data);
                updatePositions(data.data);
                // Note: drawField() is now called inside updatePositions
            }
        };

    function displayMatchEvent(event) {
        const eventsContainer = document.getElementById('results');
        const eventCard = document.createElement('div');
        eventCard.className = 'event-card animate__animated animate__fadeInLeft';

        console.log("Event received:", event);

        // Store score phase events for position tracking
        if (event.data.commentary.toLowerCase().includes('goal') ||
            event.data.commentary.toLowerCase().includes('behind') ||
            event.data.commentary.toLowerCase().includes('quarter')) {
            scorePhases.push({
                timestamp: Date.now(),
                quarter: event.quarter,
                event: event.data.commentary,
                type: getEventType(event.data.commentary),
                eventData: event
            });
            updatePhaseTimeline();
        }

        // Determine event type and icon
        let icon = '<i class="fas fa-running"></i> '; // default icon
        if (event.data.commentary.toLowerCase().includes('goal')) {
            icon = '<i class="fas fa-football-ball"></i> ';
        } else if (event.data.commentary.toLowerCase().includes('behind')) {
            icon = '<i class="fas fa-flag"></i> ';
        } else if (event.data.commentary.toLowerCase().includes('bounce')) {
            icon = '<i class="fas fa-circle"></i> ';
        } else if (event.data.commentary.toLowerCase().includes('tackle')) {
            icon = '<i class="fas fa-hand-rock"></i> ';
        }

        eventCard.innerHTML = `
            <div class="event-time">Q${event.quarter}</div>
            <div class="event-description">${icon}${event.data.commentary}</div>
        `;

        eventsContainer.appendChild(eventCard);
        eventsContainer.scrollTop = eventsContainer.scrollHeight;
    }

    function getEventType(commentary) {
        const text = commentary.toLowerCase();
        if (text.includes('goal')) return 'goal';
        if (text.includes('behind')) return 'behind';
        if (text.includes('quarter')) return 'quarter_end';
        if (text.includes('bounce')) return 'center_bounce';
        if (text.includes('out on full')) return 'out_on_full';
        if (text.includes('ball up')) return 'ball_up';
        return 'general_play';
    }

    function getCurrentPhase() {
        if (scorePhases.length === 0) {
            return 'pre_game';
        }
        const lastPhase = scorePhases[scorePhases.length - 1];
        return lastPhase.type || 'general_play';
    }

    function updateScore(data) {

        // Check the structure of the data
        if (data.home && data.home.total !== undefined) {
            // Direct access to home and away properties
            document.getElementById('home-score').textContent = data.home.total;
            document.getElementById('away-score').textContent = data.away.total;
        } else if (data.data.data && data.data.data.home && data.data.data.home.total !== undefined) {
            // Access through data.data
            document.getElementById('home-score').textContent = data.data.data.home.total;
            document.getElementById('away-score').textContent = data.data.data.away.total;
        } else {
            console.error("Unexpected score data structure:", data);
        }

        // Animate score change
        const scoreElements = document.querySelectorAll('.score');
        scoreElements.forEach(el => {
            el.classList.add('animate__animated', 'animate__pulse');
            setTimeout(() => el.classList.remove('animate__animated', 'animate__pulse'), 1000);
        });
    }

    function updateQuarter(quarter) {
        const quarterIndicator = document.getElementById('quarter');
        quarterIndicator.textContent = `Quarter ${quarter}`;
        quarterIndicator.classList.add('animate__animated', 'animate__fadeIn');
    }

    let quarterlyStats = {
    1: [], 2: [], 3: [], 4: [], all: []
    };


        // Wrap button initialization in DOMContentLoaded
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for quarter tabs
            document.querySelectorAll('.quarter-tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    const quarter = tab.dataset.quarter;
                    const activeTeam = document.querySelector('.team-filter.active')?.dataset.team || 'all';
                    console.log(`Quarter tab clicked: ${quarter}, Team: ${activeTeam}`);
                    displayFilteredStats(quarter, activeTeam);
                });
            });

            const teamFilters = document.querySelector('.team-filters');
            teamFilters.innerHTML = `
                <button class="team-filter active" data-team="all">Both Teams</button>
                <button class="team-filter" data-team="${homeTeam}">${homeTeam}</button>
                <button class="team-filter" data-team="${awayTeam}">${awayTeam}</button>
            `;

            // Add event listeners
            document.querySelectorAll('.team-filter').forEach(filter => {
                filter.addEventListener('click', () => {
                    const team = filter.dataset.team;
                    const activeQuarter = document.querySelector('.quarter-tab.active')?.dataset.quarter || 'all';
                    console.log(`Team filter clicked: ${team}, Quarter: ${activeQuarter}`);
                    displayFilteredStats(activeQuarter, team);
                });
            });
        });


        function updatePlayerStats(data) {
            console.log("Full data received:", data);  // Let's see the complete data structure

            // Check if data.data exists (since the backend might wrap it)
            const quarter = data.quarter || data.data.quarter;
            const player_stats = data.player_stats || data.data.player_stats;

            console.log("Quarter:", quarter);
            console.log("Player stats:", player_stats);

            if (!player_stats) {
                console.error("Player stats is undefined. Data structure:", data);
                return;
            }

            // Store stats for this quarter
            quarterlyStats[quarter] = player_stats;

            // Update all quarters combined stats
            quarterlyStats.all = combineQuarterStats();

            // Display the current active quarter/filter
            displayFilteredStats(quarter, 'all');

            console.log(`Updated stats for quarter ${quarter}`);
            console.log('Current quarterlyStats:', quarterlyStats);
        }

        function combineQuarterStats() {
            // Combine stats from all quarters
            const allPlayers = new Map();

            [1, 2, 3, 4].forEach(quarter => {
                const quarterStats = quarterlyStats[quarter];
                if (!quarterStats) return; // Skip if no stats for this quarter

                // Handle both array and object formats
                let playerStatsArray;
                if (Array.isArray(quarterStats)) {
                    playerStatsArray = quarterStats;
                } else {
                    // Convert object to array
                    playerStatsArray = Object.values(quarterStats);
                }

                playerStatsArray.forEach(playerStat => {
                    const playerName = playerStat.player_name || playerStat.name;
                    if (!playerName) return;

                    if (!allPlayers.has(playerName)) {
                        allPlayers.set(playerName, { ...playerStat });
                    } else {
                        const existing = allPlayers.get(playerName);
                        // Combine numerical stats
                        ['hitout', 'kick', 'handball', 'tackle', 'clearance', 'mark',
                        'goal', 'behind', 'disposal', 'turnovers_won', 'interceptions_won']
                        .forEach(stat => {
                            if (typeof playerStat[stat] === 'number' && typeof existing[stat] === 'number') {
                                existing[stat] += playerStat[stat];
                            }
                        });
                        // Recalculate efficiency
                        existing.effective_disposal = Math.round(
                            (existing.disposal > 0 ?
                                ((existing.kick + existing.handball) / existing.disposal) * 100
                                : 0)
                        );
                    }
                });
            });

            return Array.from(allPlayers.values());
        }

        function displayFilteredStats(quarter = 'all', team = 'all') {
            const stats = quarterlyStats[quarter];
            if (!stats) return; // No stats available yet

            // Handle both array and object formats
            let statsArray;
            if (Array.isArray(stats)) {
                statsArray = stats;
            } else {
                // Convert object to array
                statsArray = Object.values(stats);
            }

            const filteredStats = team === 'all' ?
                statsArray :
                statsArray.filter(player => player.team === team);

            const statsContainer = document.getElementById('player-stats');
            const statsSection = document.createElement('div');
            statsSection.className = 'stats-section animate__animated animate__fadeIn';

            let tableHTML = `
                <table class="player-stats-table">
                    <thead>
                        <tr>
                            <th onclick="sortTable(this)">Player</th>
                            <th onclick="sortTable(this)">Team</th>
                            <th onclick="sortTable(this)">Hitouts</th>
                            <th onclick="sortTable(this)">Kicks</th>
                            <th onclick="sortTable(this)">Handballs</th>
                            <th onclick="sortTable(this)">Tackles</th>
                            <th onclick="sortTable(this)">Clearances</th>
                            <th onclick="sortTable(this)">Marks</th>
                            <th onclick="sortTable(this)">Goals</th>
                            <th onclick="sortTable(this)">Behinds</th>
                            <th onclick="sortTable(this)">Disposals</th>
                            <th onclick="sortTable(this)">Eff %</th>
                            <th onclick="sortTable(this)">TO Won</th>
                            <th onclick="sortTable(this)">Int Won</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            filteredStats.forEach(stats => {
            // Debug: Log the actual stats object to see its structure
            // console.log('Individual player stats:', stats);

            // Get the actual property values, handling different possible property names
            const playerName = stats.player_name || stats.name || 'Unknown Player';
            const playerId = stats.player_id || stats.id || stats.playerId;
            const team = stats.team || 'Unknown Team';
            const hitouts = stats.hitout || stats.hitouts || 0;
            const kicks = stats.kick || stats.kicks || 0;
            const handballs = stats.handball || stats.handballs || 0;
            const tackles = stats.tackle || stats.tackles || 0;
            const clearances = stats.clearance || stats.clearances || 0;
            const marks = stats.mark || stats.marks || 0;
            const goals = stats.goal || stats.goals || 0;
            const behinds = stats.behind || stats.behinds || 0;
            const disposals = stats.disposal || stats.disposals || (kicks + handballs);
            const efficiency = stats.effective_disposal || stats.disposal_efficiency ||
                              (disposals > 0 ? Math.round(((kicks + handballs) / disposals) * 100) : 0);
            const turnoversWon = stats.turnovers_won || 0;
            const interceptionsWon = stats.interceptions_won || 0;

            // Add highlight class for impressive stats
            const isHighDisposals = disposals > 15 ? 'highlight-stat' : '';
            const isHighGoals = goals > 2 ? 'highlight-stat' : '';
            const isHighEfficiency = efficiency > 75 ? 'highlight-stat' : '';

            // Check if player is selected for tracking using player ID
            const isSelected = playerId && selectedPlayers.has(playerId);
            const selectedClass = isSelected ? 'selected-player' : '';

            tableHTML += `
                <tr class="${selectedClass}">
                    <td class="player-name-cell" onclick="togglePlayerSelection('${playerId}', '${playerName}')" title="Click to track this player">
                        ${playerName}
                        ${isSelected ? '<i class="fas fa-eye" style="margin-left: 5px; color: #003f87;"></i>' : ''}
                    </td>
                    <td>${team}</td>
                    <td>${hitouts}</td>
                    <td>${kicks}</td>
                    <td>${handballs}</td>
                    <td>${tackles}</td>
                    <td>${clearances}</td>
                    <td>${marks}</td>
                    <td class="${isHighGoals}">${goals}</td>
                    <td>${behinds}</td>
                    <td class="${isHighDisposals}">${disposals}</td>
                    <td class="${isHighEfficiency}">${efficiency}%</td>
                    <td>${turnoversWon}</td>
                    <td>${interceptionsWon}</td>
                </tr>
            `;
        });

        tableHTML += '</tbody></table>';
        statsSection.innerHTML = tableHTML;

        // Clear and update container
        statsContainer.innerHTML = '';
        statsContainer.appendChild(statsSection);

            // Update active buttons
            document.querySelectorAll('.quarter-tab').forEach(tab => {
                tab.classList.toggle('active', tab.dataset.quarter === quarter);
            });
            document.querySelectorAll('.team-filter').forEach(filter => {
                filter.classList.toggle('active', filter.dataset.team === team);
            });
        }

        // Add event listeners for filters
        document.querySelectorAll('.quarter-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const activeTeam = document.querySelector('.team-filter.active').dataset.team;
                displayFilteredStats(tab.dataset.quarter, activeTeam);
            });
        });

        document.querySelectorAll('.team-filter').forEach(filter => {
            filter.addEventListener('click', () => {
                const activeQuarter = document.querySelector('.quarter-tab.active').dataset.quarter;
                displayFilteredStats(activeQuarter, filter.dataset.team);
            });
        });

    function sortTable(header) {
    var table = header.parentElement.parentElement.parentElement;
    var rows = Array.from(table.querySelectorAll('tr:nth-child(n+2)')); // Skip the header row
    var index = Array.from(header.parentElement.children).indexOf(header);
    var isNumeric = !isNaN(rows[0].children[index].innerText.trim());
    var ascending = !header.classList.contains('asc');

    rows.sort(function(rowA, rowB) {
        var cellA = rowA.children[index].innerText.trim();
        var cellB = rowB.children[index].innerText.trim();

        if (isNumeric) {
            cellA = parseFloat(cellA);
            cellB = parseFloat(cellB);
        }

        if (ascending) {
            return cellA > cellB ? 1 : -1;
        } else {
            return cellA < cellB ? 1 : -1;
        }
    });

    rows.forEach(function(row) {
        table.appendChild(row);
    });

    // Update the class for sorting indication
    Array.from(header.parentElement.children).forEach(function(th) {
        th.classList.remove('asc', 'desc');
    });
    header.classList.add(ascending ? 'asc' : 'desc');
}

// Player tracking functions
function togglePlayerSelection(playerId, playerName) {
    console.log('Toggling selection for player:', playerName, 'ID:', playerId);
    console.log('Current selected players:', Array.from(selectedPlayers));
    console.log('Available players in position history:', Array.from(positionHistory.keys()));
    console.log('Current players on field:', Array.from(players.keys()));

    // Use player ID for tracking
    if (!playerId) {
        console.warn(`Cannot track player "${playerName}" - no player ID provided`);
        alert(`Cannot track player "${playerName}" - no player ID available.`);
        return;
    }

    // Convert player ID to string for consistency
    const playerIdString = String(playerId);

    // Debug: Show what player IDs we have in position history vs what we're looking for
    console.log('=== PLAYER SELECTION DEBUG ===');
    console.log('All position history player IDs:', Array.from(positionHistory.keys()));
    console.log('Current players on field:', Array.from(players.keys()));
    console.log('Available players in position history:', Array.from(positionHistory.keys()));
    console.log('Looking for player ID from stats:', playerId, '(type:', typeof playerId, ')');
    console.log('Converted to string:', playerIdString, '(type:', typeof playerIdString, ')');
    console.log('Player name from stats:', playerName);

    // Check if we have position data for this player ID (using string)
    if (!positionHistory.has(playerIdString)) {
        console.warn(`Cannot track player "${playerName}" (ID: ${playerIdString}) - no position data found`);
        console.log('Available position history keys:', Array.from(positionHistory.keys()));
        alert(`Cannot track player "${playerName}" - no position data available yet. Please wait for more position updates.`);
        return;
    }

    console.log('SUCCESS: Found position data for player ID:', playerIdString);

    // Use player ID string for selection
    if (selectedPlayers.has(playerIdString)) {
        selectedPlayers.delete(playerIdString);
        console.log('Deselected player:', playerName, 'ID:', playerIdString);
    } else {
        selectedPlayers.add(playerIdString);
        console.log('Selected player:', playerName, 'ID:', playerIdString);
    }

    updateSelectedPlayersInfo();
    updatePlayerStatsDisplay();
    updateLegendVisibility();
    drawField(); // Redraw field to show/hide selected players
}

function clearPlayerSelection() {
    selectedPlayers.clear();
    updateSelectedPlayersInfo();
    updatePlayerStatsDisplay();
    updateLegendVisibility();
    drawField();
}

function updateSelectedPlayersInfo() {
    const infoElement = document.getElementById('selected-players-info');
    const clearButton = document.getElementById('clear-selection');

    if (selectedPlayers.size === 0) {
        infoElement.textContent = 'No players selected';
        clearButton.style.display = 'none';
    } else {
        // Convert player IDs to names for display
        const playerNames = Array.from(selectedPlayers).map(playerId => {
            // Get name from ID mapping
            const nameFromId = playerIdToNameMap.get(playerId);
            if (nameFromId) return nameFromId;

            // Try to find name from position history
            const history = positionHistory.get(playerId);
            if (history && history.length > 0 && history[0].playerName) {
                return history[0].playerName;
            }

            return `Player ${playerId}`;
        });
        infoElement.textContent = `Tracking: ${playerNames.join(', ')}`;
        clearButton.style.display = 'inline-block';
    }
}

function updatePlayerStatsDisplay() {
    // Refresh the current stats display to show selection status
    const activeQuarter = document.querySelector('.quarter-tab.active')?.dataset.quarter || 'all';
    const activeTeam = document.querySelector('.team-filter.active')?.dataset.team || 'all';
    displayFilteredStats(activeQuarter, activeTeam);
}

function updateLegendVisibility() {
    const selectedLegend = document.getElementById('selected-player-legend');
    const trailLegend = document.getElementById('trail-legend');

    if (selectedPlayers.size > 0) {
        selectedLegend.style.display = 'flex';
        trailLegend.style.display = 'flex';
    } else {
        selectedLegend.style.display = 'none';
        trailLegend.style.display = 'none';
    }
}

// View mode functions
function setLiveView() {
    isLiveView = true;
    currentPhaseIndex = -1;

    document.getElementById('live-view-btn').classList.add('active');
    document.getElementById('history-view-btn').classList.remove('active');
    document.getElementById('phase-timeline').style.display = 'none';
    document.getElementById('current-phase-info').textContent = 'Live View';

    drawField();
}

function setHistoryView() {
    isLiveView = false;

    document.getElementById('live-view-btn').classList.remove('active');
    document.getElementById('history-view-btn').classList.add('active');
    document.getElementById('phase-timeline').style.display = 'block';

    // Show the most recent phase by default
    if (scorePhases.length > 0) {
        currentPhaseIndex = scorePhases.length - 1;
        updateHistoryView();
    }
}

// Phase navigation functions
function previousPhase() {
    if (currentPhaseIndex > 0) {
        currentPhaseIndex--;
        updateHistoryView();
    }
}

function nextPhase() {
    if (currentPhaseIndex < scorePhases.length - 1) {
        currentPhaseIndex++;
        updateHistoryView();
    }
}

function selectPhase(phaseIndex) {
    currentPhaseIndex = phaseIndex;
    updateHistoryView();
}

function updateHistoryView() {
    if (currentPhaseIndex >= 0 && currentPhaseIndex < scorePhases.length) {
        const phase = scorePhases[currentPhaseIndex];
        document.getElementById('current-phase-info').textContent =
            `${phase.type.toUpperCase()} - Q${phase.quarter}`;

        // Update timeline event highlighting
        updateTimelineHighlight();

        // Update navigation buttons
        document.getElementById('prev-phase-btn').disabled = currentPhaseIndex === 0;
        document.getElementById('next-phase-btn').disabled = currentPhaseIndex === scorePhases.length - 1;

        // Load historical positions for this phase
        loadHistoricalPositions(phase.timestamp);
    }
}

function updatePhaseTimeline() {
    const timelineContainer = document.getElementById('timeline-events');
    timelineContainer.innerHTML = '';

    scorePhases.forEach((phase, index) => {
        const eventElement = document.createElement('div');
        eventElement.className = 'timeline-event';
        eventElement.onclick = () => selectPhase(index);

        eventElement.innerHTML = `
            <div class="timeline-event-type">${phase.type.toUpperCase()}</div>
            <div class="timeline-event-desc">Q${phase.quarter}</div>
        `;

        timelineContainer.appendChild(eventElement);
    });

    updateTimelineHighlight();
}

function updateTimelineHighlight() {
    const events = document.querySelectorAll('.timeline-event');
    events.forEach((event, index) => {
        event.classList.toggle('active', index === currentPhaseIndex);
    });
}

function loadHistoricalPositions(timestamp) {
    // Clear current positions
    players.clear();
    ballPosition = null;

    // Find positions closest to the given timestamp for each player
    positionHistory.forEach((history, playerName) => {
        // Find the position closest to the timestamp
        let closestPosition = null;
        let minTimeDiff = Infinity;

        for (const posRecord of history) {
            const timeDiff = Math.abs(posRecord.timestamp - timestamp);
            if (timeDiff < minTimeDiff) {
                minTimeDiff = timeDiff;
                closestPosition = posRecord;
            }
        }

        if (closestPosition && minTimeDiff < 30000) { // Within 30 seconds
            if (playerName === 'ball') {
                ballPosition = closestPosition.position;
            } else {
                players.set(playerName, {
                    position: closestPosition.position,
                    team: closestPosition.team
                });
            }
        }
    });

    drawField();
}

    socket.onclose = (event) => console.log("WebSocket closed:", event);
    socket.onerror = (error) => console.error("WebSocket error:", error);






    </script>
</head>
<body>
    <div class="match-container">
        <div class="scoreboard">
            <div class="team-score">
                <img src="{{ home_team_logo }}" alt="Home Team" class="team-logo">
                <div class="team-name">{{ match.home_team.name }}</div>
                <div class="score" id="home-score">0</div>
            </div>
            <div class="versus">VS</div>
            <div class="team-score">
                <img src="{{ away_team_logo }}" alt="Away Team" class="team-logo">
                <div class="team-name">{{ match.away_team.name }}</div>
                <div class="score" id="away-score">0</div>
            </div>
        </div>

        <div class="quarter-indicator" id="quarter">Quarter 1</div>

        <div class="match-events" id="results">
            <div class="loading">
                <div class="loading-spinner"></div>
                <p>Waiting for match to begin...</p>
            </div>
        </div>

        <div class="field-container">
            <div class="field-header">
                <h2>Live Field View</h2>
                <div class="player-tracking-controls">
                    <div class="tracking-info">
                        <span id="selected-players-info">No players selected</span>
                        <button id="clear-selection" onclick="clearPlayerSelection()" style="display: none;">Clear Selection</button>
                    </div>
                    <div class="view-mode-controls">
                        <button id="live-view-btn" class="view-mode-btn active" onclick="setLiveView()">Live View</button>
                        <button id="history-view-btn" class="view-mode-btn" onclick="setHistoryView()">History View</button>
                    </div>
                </div>
            </div>

            <div class="phase-timeline" id="phase-timeline" style="display: none;">
                <div class="timeline-header">
                    <h4>Score Events Timeline</h4>
                    <div class="timeline-controls">
                        <button onclick="previousPhase()" id="prev-phase-btn">← Previous</button>
                        <span id="current-phase-info">Live View</span>
                        <button onclick="nextPhase()" id="next-phase-btn">Next →</button>
                    </div>
                </div>
                <div class="timeline-events" id="timeline-events">
                    <!-- Timeline events will be populated here -->
                </div>
            </div>

            <div class="field-visualization">
                <canvas id="fieldCanvas"></canvas>
            </div>
            <div class="field-legend">
                <div class="legend-item">
                    <div class="legend-dot home"></div>
                    <span>{{ match.home_team.name }}</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot away"></div>
                    <span>{{ match.away_team.name }}</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot ball"></div>
                    <span>Ball</span>
                </div>
                <div class="legend-item" id="selected-player-legend" style="display: none;">
                    <div class="legend-dot selected"></div>
                    <span>Selected Player(s)</span>
                </div>
                <div class="legend-item" id="trail-legend" style="display: none;">
                    <div class="legend-line trail"></div>
                    <span>Movement Trail</span>
                </div>
            </div>
        </div>

        <div class="stats-container">
            <div class="stats-header">
                <h2>Player Statistics</h2>
                <div class="stats-controls">
                    <div class="quarter-tabs">
                        <button class="quarter-tab" data-quarter="all">All Quarters</button>
                        <button class="quarter-tab" data-quarter="1">Q1</button>
                        <button class="quarter-tab" data-quarter="2">Q2</button>
                        <button class="quarter-tab" data-quarter="3">Q3</button>
                        <button class="quarter-tab" data-quarter="4">Q4</button>
                    </div>
                    <div class="team-filters">
                        <button class="team-filter active" data-team="all">Both Teams</button>
                        <button class="team-filter" data-team="team1">{{ home_team }}</button>
                        <button class="team-filter" data-team="team2">{{ away_team }}</button>
                    </div>
                </div>
            </div>
            <div id="player-stats"></div>
        </div>
    </div>
</html>
<style>
    .match-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .scoreboard {
        background: linear-gradient(135deg, #003f87 0%, #0056b3 100%);
        border-radius: 15px;
        padding: 20px;
        color: white;
        margin-bottom: 20px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .team-score {
        text-align: center;
        flex: 1;
    }

    .team-logo {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 3px solid white;
        margin: 10px;
        transition: transform 0.3s ease;
    }

    .team-logo:hover {
        transform: scale(1.1);
    }

    .score {
        font-size: 3em;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
    }

    .score.highlight {
    color: #ffd700;
    transform: scale(1.1);
    }

    .versus {
        font-size: 2em;
        margin: 0 20px;
        color: #ffd700;
    }

    .match-events {
        background: #e8f0fa;
        border-radius: 15px;
        padding: 20px;
        height: 400px;
        overflow-y: auto;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .event-card {
        transform-origin: left;
        /*animation: slideIn 0.5s ease;*/

        display: flex;
        align-items: center;
        gap: 10px;
        background: white;
        margin: 10px 0;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }


    .event-time {
        background: #003f87;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
    }

    .event-description {
        flex-grow: 1;
    }

    .event-description i {
        margin-right: 8px;
        color: #003f87;
    }

    @keyframes slideIn {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .quarter-indicator {
        position: sticky;
        top: 0;
        background: #003f87;
        color: white;
        padding: 10px;
        border-radius: 10px;
        margin: 10px 0;
        text-align: center;
        z-index: 100;
    }
    /*
    .stats-container {
        margin-top: 20px;
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .player-stats-table {
        width: 100%;
        border-collapse: collapse;
    }

    .player-stats-table th {
        background: #003f87;
        color: white;
        padding: 12px;
        cursor: pointer;
    }

    .player-stats-table td {
        padding: 10px;
        border-bottom: 1px solid #eee;
    }

    .player-stats-table tr:hover {
        background: #f5f8ff;
    }

    .player-stats-table th.asc::after {
    content: ' ↑';
    color: #ffd700;
    }

    .player-stats-table th.desc::after {
    content: ' ↓';
    color: #ffd700;
    }

    .stats-section {
    margin-top: 20px;
    } */

    /* Add loading animation */
    .loading {
        text-align: center;
        padding: 20px;
    }

    .loading-spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #003f87;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .stats-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-top: 30px;
    }

    .stats-header {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-bottom: 20px;
    }

    .stats-controls {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .quarter-tabs, .team-filters {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .quarter-tab, .team-filter {
        padding: 8px 16px;
        border: none;
        border-radius: 20px;
        background: #f0f0f0;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .quarter-tab:hover, .team-filter:hover {
        background: #e0e0e0;
    }

    .quarter-tab.active, .team-filter.active {
        background: #003f87;
        color: white;
    }

    .player-stats-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 20px;
    overflow: hidden;
    }

    .player-stats-table th {
        background: #003f87;
        color: white;
        padding: 15px;
        text-align: left;
        font-weight: 500;
        position: sticky;
        top: 0;
        z-index: 10;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .player-stats-table th:hover {
        background: #004fa8;
    }

    .player-stats-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
    }

    .player-stats-table tbody tr {
        transition: background-color 0.3s ease;
    }

    .player-stats-table tbody tr:hover {
        background: #f8f9ff;
    }

    .highlight-stat {
        color: #003f87;
        font-weight: bold;
    }

    .stats-section {
        opacity: 0;
        transform: translateY(20px);
        animation: slideIn 0.5s forwards;
    }

    @keyframes slideIn {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .field-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .field-container h2 {
        margin-bottom: 15px;
        color: #003f87;
    }

    .field-visualization {
        position: relative;
        width: 100%;
        /* Set aspect ratio to match AFL field dimensions (160m x 130m) */
        height: 0;
        padding-bottom: 81.25%; /* 130/160 = 0.8125 or 81.25% */
        background: #4a8f29; /* Base grass color */
        border-radius: 50%; /* More oval-like shape */
        overflow: hidden;
        border: 2px solid #2d5619;
        box-shadow: inset 0 0 50px rgba(0,0,0,0.2);
    }

    #fieldCanvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
    }

    /* Remove the overlay elements as we'll draw everything on canvas */
    .field-overlay {
        display: none;
    }

    .field-legend {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 15px;
        padding: 10px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .legend-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .legend-dot.home {
        background: #003f87;
    }

    .legend-dot.away {
        background: #e31837;
    }

    .legend-dot.ball {
        background: #ffffff;
        border: 1px solid #000;
    }

    .legend-dot.selected {
        background: #ffd700;
        border: 2px solid #ff6600;
    }

    .legend-line.trail {
        width: 20px;
        height: 3px;
        background: linear-gradient(to right, #ffd700, #ff6600);
        border-radius: 2px;
    }

    /* Player tracking controls */
    .field-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .player-tracking-controls {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }

    .tracking-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .view-mode-controls {
        display: flex;
        gap: 5px;
    }

    .view-mode-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 20px;
        background: #f0f0f0;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .view-mode-btn:hover {
        background: #e0e0e0;
    }

    .view-mode-btn.active {
        background: #003f87;
        color: white;
    }

    #clear-selection {
        padding: 5px 10px;
        border: none;
        border-radius: 15px;
        background: #e31837;
        color: white;
        cursor: pointer;
        font-size: 12px;
    }

    #clear-selection:hover {
        background: #c41230;
    }

    /* Phase timeline */
    .phase-timeline {
        background: #f8f9ff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid #e0e0e0;
    }

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .timeline-header h4 {
        margin: 0;
        color: #003f87;
    }

    .timeline-controls {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .timeline-controls button {
        padding: 5px 10px;
        border: none;
        border-radius: 15px;
        background: #003f87;
        color: white;
        cursor: pointer;
        font-size: 12px;
    }

    .timeline-controls button:hover {
        background: #004fa8;
    }

    .timeline-controls button:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    #current-phase-info {
        font-weight: bold;
        color: #003f87;
        min-width: 120px;
        text-align: center;
    }

    .timeline-events {
        display: flex;
        gap: 10px;
        overflow-x: auto;
        padding: 10px 0;
    }

    .timeline-event {
        min-width: 120px;
        padding: 8px 12px;
        background: white;
        border-radius: 8px;
        border: 2px solid #e0e0e0;
        cursor: pointer;
        text-align: center;
        transition: all 0.3s ease;
    }

    .timeline-event:hover {
        border-color: #003f87;
    }

    .timeline-event.active {
        border-color: #003f87;
        background: #003f87;
        color: white;
    }

    .timeline-event-type {
        font-weight: bold;
        font-size: 12px;
    }

    .timeline-event-desc {
        font-size: 11px;
        margin-top: 2px;
    }

    /* Player selection styles */
    .player-name-cell {
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .player-name-cell:hover {
        background: #f0f8ff;
        color: #003f87;
        font-weight: bold;
    }

    .selected-player {
        background: #fff3cd !important;
        border-left: 4px solid #ffd700;
    }

    .selected-player .player-name-cell {
        color: #003f87;
        font-weight: bold;
    }

</style>

<script>
// Add new field visualization code
// Initialize global variables first
let canvas, ctx;
let players = new Map();
let ballPosition = null;

// Player tracking system variables
let positionHistory = new Map(); // Map of player IDs to array of position records
let scorePhases = []; // Array of score events with timestamps
let selectedPlayers = new Set(); // Set of selected player IDs for tracking
let selectedPhase = null; // Currently selected phase for viewing
let currentPhaseIndex = -1; // Index of current phase being viewed (-1 = live view)
let isLiveView = true; // Whether we're viewing live or historical data
let playerIdToNameMap = new Map(); // Map player IDs to names for display

// Initialize field visualization when document is loaded
document.addEventListener('DOMContentLoaded', function() {
    canvas = document.getElementById('fieldCanvas');
    ctx = canvas.getContext('2d');

    // Set canvas size to match container
    function resizeCanvas() {
        const container = canvas.parentElement;
        canvas.width = container.offsetWidth;
        canvas.height = container.offsetHeight;
        if (ctx) drawField(); // Only draw if context exists
    }

    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();
});

function updatePositions(data) {
    // Check for nested data structure
    if (!data || !data.data || !data.data.positions || !Array.isArray(data.data.positions)) {
        console.error('Invalid positions data:', data);
        return;
    }

    // Only update live positions if we're in live view mode
    if (!isLiveView) {
        return; // Don't update positions when viewing historical data
    }

    // Clear existing positions
    players.clear();

    // Get current timestamp and phase info
    const timestamp = Date.now();
    const currentPhase = getCurrentPhase();

    // Access the nested positions array
    data.data.positions.forEach(pos => {
        if (!pos || !pos.player_name || !pos.position) return;

        // Log position data for debugging
        // console.log(`Position update for ${pos.player_name}: x=${pos.position.x}, y=${pos.position.y}, team=${pos.team}`);

        if (pos.player_name === 'ball') {
            ballPosition = {
                x: pos.position.x,
                y: pos.position.y
            };

            // Store ball position history
            if (!positionHistory.has('ball')) {
                positionHistory.set('ball', []);
            }
            positionHistory.get('ball').push({
                position: { x: pos.position.x, y: pos.position.y },
                timestamp: timestamp,
                phase: currentPhase,
                team: null
            });
        } else {
            const playerId = pos.player_id || pos.id;
            const playerName = pos.player_name || pos.name;

            console.log('Position update for player:', playerName, 'ID:', playerId, 'Team:', pos.team);

            // Store the ID to name mapping (convert to string for consistency)
            if (playerId && playerName) {
                const playerIdString = String(playerId);
                playerIdToNameMap.set(playerIdString, playerName);
                console.log('Stored ID to name mapping:', playerIdString, '->', playerName);
            }

            // Use player name as key for the players map (for field display)
            players.set(playerName, {
                position: {
                    x: pos.position.x,
                    y: pos.position.y
                },
                team: pos.team,
                id: playerId
            });

            // Store player position history using player ID only (convert to string for consistency)
            if (!playerId) {
                console.error('No player ID found for position history!', pos);
                return;
            }

            // Convert player ID to string to ensure consistent key type
            const playerIdString = String(playerId);
            console.log('Using player ID for position history:', playerIdString, 'for player:', playerName);

            if (!positionHistory.has(playerIdString)) {
                positionHistory.set(playerIdString, []);
                console.log('Created NEW position history for player:', playerName, 'with ID:', playerIdString);
            }

            const positionRecord = {
                position: { x: pos.position.x, y: pos.position.y },
                timestamp: timestamp,
                phase: currentPhase,
                team: pos.team,
                playerId: playerIdString,
                playerName: playerName
            };

            positionHistory.get(playerIdString).push(positionRecord);
            console.log('Added position record for', playerName, 'ID:', playerIdString, '- total records:', positionHistory.get(playerIdString).length);
        }
    });

    // Limit history size to prevent memory issues (keep last 1000 positions per player)
    positionHistory.forEach((history, playerName) => {
        if (history.length > 1000) {
            history.splice(0, history.length - 1000);
        }
    });

    // Redraw the field with updated positions
    drawField();
}

function drawField() {
    if (!ctx) return; // Guard clause if context isn't ready

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // Calculate dimensions based on AFL ground (160m x 130m)
    // Use the shorter dimension to ensure the field fits
    const fieldRatio = 160 / 130; // length/width
    const maxWidth = canvas.width * 0.95;
    const maxHeight = canvas.height * 0.95;

    let fieldWidth, fieldHeight;

    if (maxWidth / fieldRatio <= maxHeight) {
        // Width is the limiting factor
        fieldWidth = maxWidth;
        fieldHeight = fieldWidth / fieldRatio;
    } else {
        // Height is the limiting factor
        fieldHeight = maxHeight;
        fieldWidth = fieldHeight * fieldRatio;
    }

    // Calculate the oval dimensions
    const ovalWidth = fieldWidth * 0.95;
    const ovalHeight = fieldHeight * 0.95;

    // Draw grass background (lighter in the center, darker at edges)
    const gradient = ctx.createRadialGradient(
        centerX, centerY, 0,
        centerX, centerY, Math.max(ovalWidth, ovalHeight) / 1.5
    );
    gradient.addColorStop(0, '#5da03a'); // Lighter green in center
    gradient.addColorStop(1, '#3d7023'); // Darker green at edges

    // Create clipping path for the oval
    ctx.beginPath();
    ctx.ellipse(
        centerX,
        centerY,
        ovalWidth / 2,
        ovalHeight / 2,
        0,
        0,
        2 * Math.PI
    );

    // Save the current context state
    ctx.save();

    // Create a clipping region in the shape of the oval
    ctx.clip();

    // Fill the oval with gradient
    ctx.fillStyle = gradient;
    ctx.fillRect(centerX - ovalWidth/2, centerY - ovalHeight/2, ovalWidth, ovalHeight);

    // Restore the context (removes the clipping region)
    ctx.restore();

    // Draw the oval outline
    ctx.beginPath();
    ctx.ellipse(
        centerX,
        centerY,
        ovalWidth / 2,
        ovalHeight / 2,
        0,
        0,
        2 * Math.PI
    );
    ctx.strokeStyle = 'rgba(255,255,255,0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Draw center line with proper clipping
    // Save context for clipping
    ctx.save();

    // Create clipping path for the oval
    ctx.beginPath();
    ctx.ellipse(
        centerX,
        centerY,
        ovalWidth / 2,
        ovalHeight / 2,
        0,
        0,
        2 * Math.PI
    );
    ctx.clip();

 

    // Restore context (remove clipping)
    ctx.restore();

    // Draw center circle (3m radius in AFL)
    const centerCircleRadius = (3 / 160) * ovalWidth; // Scale to field size
    ctx.beginPath();
    ctx.arc(centerX, centerY, centerCircleRadius, 0, 2 * Math.PI);
    ctx.strokeStyle = 'rgba(255,255,255,0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();



    // Draw 50m arcs using goal posts as reference points

    // Save context for clipping
    ctx.save();

    // Create clipping path for the oval
    ctx.beginPath();
    ctx.ellipse(
        centerX,
        centerY,
        ovalWidth / 2,
        ovalHeight / 2,
        0,
        0,
        2 * Math.PI
    );
    ctx.clip();

    // Calculate the 50m distance scaled to our field
    const fiftyMeters = (50 / 160) * ovalWidth;

    // Left (defensive) 50m arc
    // Use the center of the left goal posts as the reference point
    const leftGoalX = centerX - ovalWidth/2;
    ctx.beginPath();
    ctx.arc(
        leftGoalX,
        centerY,
        fiftyMeters,
        -Math.PI/2,  // Start at top
        Math.PI/2,   // End at bottom
        false        // Draw clockwise
    );
    ctx.strokeStyle = 'rgba(255,255,255,0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Right (forward) 50m arc
    // Use the center of the right goal posts as the reference point
    const rightGoalX = centerX + ovalWidth/2;
    ctx.beginPath();
    ctx.arc(
        rightGoalX,
        centerY,
        fiftyMeters,
        Math.PI/2,   // Start at bottom
        -Math.PI/2,  // End at top
        false        // Draw clockwise
    );
    ctx.strokeStyle = 'rgba(255,255,255,0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Add subtle 50m zone shading
    // Left 50m zone
    ctx.beginPath();
    ctx.arc(
        leftGoalX,
        centerY,
        fiftyMeters,
        -Math.PI/2,
        Math.PI/2,
        false
    );
    ctx.lineTo(leftGoalX, centerY + ovalHeight/2);
    ctx.lineTo(leftGoalX, centerY - ovalHeight/2);
    ctx.closePath();
    ctx.fillStyle = 'rgba(255,255,255,0.03)';
    ctx.fill();

    // Right 50m zone
    ctx.beginPath();
    ctx.arc(
        rightGoalX,
        centerY,
        fiftyMeters,
        Math.PI/2,
        -Math.PI/2,
        false
    );
    ctx.lineTo(rightGoalX, centerY - ovalHeight/2);
    ctx.lineTo(rightGoalX, centerY + ovalHeight/2);
    ctx.closePath();
    ctx.fillStyle = 'rgba(255,255,255,0.03)';
    ctx.fill();

    // Restore context (remove clipping)
    ctx.restore();

    // Draw center square (official AFL center square is 45m x 45m)
    // Save context for clipping
    ctx.save();

    // Create clipping path for the oval
    ctx.beginPath();
    ctx.ellipse(
        centerX,
        centerY,
        ovalWidth / 2,
        ovalHeight / 2,
        0,
        0,
        2 * Math.PI
    );
    ctx.clip();

    const centerSquareSize = (45 / 160) * ovalWidth; // Scale to field size
    ctx.beginPath();
    ctx.rect(
        centerX - centerSquareSize/2,
        centerY - centerSquareSize/2,
        centerSquareSize,
        centerSquareSize
    );
    ctx.strokeStyle = 'rgba(255,255,255,0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Restore context (remove clipping)
    ctx.restore();

    // Draw goal squares (6.4m wide x 9m deep in AFL)
    // Save context for clipping
    ctx.save();

    // Create clipping path for the oval
    ctx.beginPath();
    ctx.ellipse(
        centerX,
        centerY,
        ovalWidth / 2,
        ovalHeight / 2,
        0,
        0,
        2 * Math.PI
    );
    ctx.clip();

    const goalSquareWidth = (6.4 / 130) * ovalHeight;
    const goalSquareDepth = (9 / 160) * ovalWidth;

    // Left goal square
    ctx.beginPath();
    ctx.rect(
        centerX - ovalWidth/2,
        centerY - goalSquareWidth/2,
        goalSquareDepth,
        goalSquareWidth
    );
    ctx.strokeStyle = 'rgba(255,255,255,0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Right goal square
    ctx.beginPath();
    ctx.rect(
        centerX + ovalWidth/2 - goalSquareDepth,
        centerY - goalSquareWidth/2,
        goalSquareDepth,
        goalSquareWidth
    );
    ctx.strokeStyle = 'rgba(255,255,255,0.8)';
    ctx.lineWidth = 2;
    ctx.stroke();

    // Restore context (remove clipping)
    ctx.restore();

    // Draw goal posts
    // In AFL, there are 4 posts at each end - 2 taller goal posts in the middle
    // and 2 shorter behind posts on the outside
    const postWidth = 3;
    const goalPostSpacing = (6.4 / 130) * ovalHeight; // Distance between goal posts
    const behindPostSpacing = (19.2 / 130) * ovalHeight; // Distance between behind posts (3x goal post spacing)

    // Left goal posts (4 posts total)
    // Draw the two goal posts (taller, in the middle)
    for (let i = -1; i <= 1; i += 2) {
        ctx.beginPath();
        ctx.rect(
            centerX - ovalWidth/2 - postWidth/2,
            centerY + i * (goalPostSpacing/2) - postWidth/2,
            postWidth,
            postWidth
        );
        ctx.fillStyle = 'white';
        ctx.fill();
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 1;
        ctx.stroke();
    }

    // Draw the two behind posts (shorter, on the outside)
    for (let i = -1; i <= 1; i += 2) {
        ctx.beginPath();
        ctx.rect(
            centerX - ovalWidth/2 - postWidth/2,
            centerY + i * (behindPostSpacing/2) - postWidth/2,
            postWidth,
            postWidth
        );
        ctx.fillStyle = 'white';
        ctx.fill();
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 1;
        ctx.stroke();
    }

    // Right goal posts (4 posts total)
    // Draw the two goal posts (taller, in the middle)
    for (let i = -1; i <= 1; i += 2) {
        ctx.beginPath();
        ctx.rect(
            centerX + ovalWidth/2 - postWidth/2,
            centerY + i * (goalPostSpacing/2) - postWidth/2,
            postWidth,
            postWidth
        );
        ctx.fillStyle = 'white';
        ctx.fill();
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 1;
        ctx.stroke();
    }

    // Draw the two behind posts (shorter, on the outside)
    for (let i = -1; i <= 1; i += 2) {
        ctx.beginPath();
        ctx.rect(
            centerX + ovalWidth/2 - postWidth/2,
            centerY + i * (behindPostSpacing/2) - postWidth/2,
            postWidth,
            postWidth
        );
        ctx.fillStyle = 'white';
        ctx.fill();
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 1;
        ctx.stroke();
    }

    // Draw player trails for selected players (only in history view or if trails are enabled)
    if (!isLiveView || selectedPlayers.size > 0) {
        drawPlayerTrails();
    }

    // Draw players
    players.forEach((data, name) => {
        if (!data.position || typeof data.position.x === 'undefined' || typeof data.position.y === 'undefined') return;

        // Map grid coordinates to field coordinates
        // The Ground class uses grid_length = 80, grid_width = 65
        // We need to map these to our oval dimensions
        const fieldX = data.position.x / 80; // Convert to 0-1 range
        const fieldY = data.position.y / 65; // Convert to 0-1 range

        // Calculate position on the oval
        // Center is at (0.5, 0.5) in normalized coordinates
        const x = centerX + (fieldX - 0.5) * ovalWidth;
        const y = centerY + (fieldY - 0.5) * ovalHeight;

        // Check if the position is within the oval boundary
        const isInOval = ((x - centerX) ** 2) / ((ovalWidth / 2) ** 2) +
                         ((y - centerY) ** 2) / ((ovalHeight / 2) ** 2) <= 1;

        if (!isInOval) return; // Skip players outside the field

        // Check if this player is selected (check by player ID only)
        const playerData = players.get(name);
        const playerId = playerData?.id;
        const isSelected = playerId ? selectedPlayers.has(playerId) : false;

        // Draw player circle with team color and shadow
        ctx.beginPath();
        ctx.shadowColor = 'rgba(0,0,0,0.5)';
        ctx.shadowBlur = 5;

        // Use different styling for selected players
        if (isSelected) {
            ctx.arc(x, y, 12, 0, Math.PI * 2); // Larger circle for selected players
            ctx.fillStyle = '#ffd700'; // Gold color for selected players
            ctx.fill();
            ctx.strokeStyle = '#ff6600';
            ctx.lineWidth = 3;
            ctx.stroke();
        } else {
            ctx.arc(x, y, 8, 0, Math.PI * 2);
            ctx.fillStyle = data.team === 'home' ? '#003f87' : '#e31837';
            ctx.fill();
        }
        ctx.shadowBlur = 0;

        // Draw player name with background
        let playerName = name;
        // Try to extract first name and number if available
        const nameParts = name.split(' ');
        if (nameParts.length > 1) {
            const lastPart = nameParts[nameParts.length - 1];
            if (lastPart.includes('-')) {
                // Format like "FirstName LastName-Number"
                playerName = nameParts[0] + " " + lastPart.split('-')[1];
            } else {
                // Just use first name to keep it short
                playerName = nameParts[0];
            }
        }

        ctx.font = isSelected ? 'bold 14px Arial' : 'bold 12px Arial';
        const textWidth = ctx.measureText(playerName).width;

        // Draw name background with different color for selected players
        ctx.fillStyle = isSelected ? 'rgba(255, 215, 0, 0.8)' : 'rgba(0,0,0,0.6)';
        ctx.fillRect(x - textWidth/2 - 4, y - (isSelected ? 30 : 25), textWidth + 8, 16);

        // Draw name text
        ctx.fillStyle = isSelected ? 'black' : 'white';
        ctx.textAlign = 'center';
        ctx.fillText(playerName, x, y - (isSelected ? 18 : 13));
    });

    // Draw ball
    if (ballPosition && typeof ballPosition.x !== 'undefined' && typeof ballPosition.y !== 'undefined') {
        // Map grid coordinates to field coordinates
        const fieldX = ballPosition.x / 80; // Convert to 0-1 range
        const fieldY = ballPosition.y / 65; // Convert to 0-1 range

        // Calculate position on the oval
        const x = centerX + (fieldX - 0.5) * ovalWidth;
        const y = centerY + (fieldY - 0.5) * ovalHeight;

        // Check if the ball is within the oval boundary
        const isInOval = ((x - centerX) ** 2) / ((ovalWidth / 2) ** 2) +
                         ((y - centerY) ** 2) / ((ovalHeight / 2) ** 2) <= 1;

        if (isInOval) {
            // Draw ball with shadow
            ctx.beginPath();
            ctx.shadowColor = 'rgba(0,0,0,0.5)';
            ctx.shadowBlur = 5;
            ctx.arc(x, y, 5, 0, Math.PI * 2);
            ctx.fillStyle = '#ffffff';
            ctx.strokeStyle = '#000000';
            ctx.fill();
            ctx.stroke();
            ctx.shadowBlur = 0;
        }
    }
}

function drawPlayerTrails() {
    if (selectedPlayers.size === 0) return;

    selectedPlayers.forEach(playerId => {
        const history = positionHistory.get(playerId);
        if (!history || history.length < 2) return;

        // Get recent positions (last 50 positions or positions from current phase)
        let positions = history;
        if (isLiveView) {
            // In live view, show last 50 positions
            positions = history.slice(-50);
        } else {
            // In history view, show positions around the selected phase
            const currentPhase = scorePhases[currentPhaseIndex];
            if (currentPhase) {
                const phaseTime = currentPhase.timestamp;
                positions = history.filter(pos =>
                    Math.abs(pos.timestamp - phaseTime) < 60000 // Within 1 minute
                );
            }
        }

        if (positions.length < 2) return;

        // Draw trail
        ctx.beginPath();
        ctx.strokeStyle = '#ffd700';
        ctx.lineWidth = 3;
        ctx.globalAlpha = 0.7;

        let firstPoint = true;
        positions.forEach((posRecord, index) => {
            const fieldX = posRecord.position.x / 80;
            const fieldY = posRecord.position.y / 65;
            const x = centerX + (fieldX - 0.5) * ovalWidth;
            const y = centerY + (fieldY - 0.5) * ovalHeight;

            // Check if position is within oval
            const isInOval = ((x - centerX) ** 2) / ((ovalWidth / 2) ** 2) +
                             ((y - centerY) ** 2) / ((ovalHeight / 2) ** 2) <= 1;

            if (isInOval) {
                if (firstPoint) {
                    ctx.moveTo(x, y);
                    firstPoint = false;
                } else {
                    ctx.lineTo(x, y);
                }

                // Draw small dots along the trail
                if (index % 5 === 0) { // Every 5th position
                    ctx.save();
                    ctx.beginPath();
                    ctx.arc(x, y, 2, 0, Math.PI * 2);
                    ctx.fillStyle = '#ff6600';
                    ctx.fill();
                    ctx.restore();
                }
            }
        });

        ctx.stroke();
        ctx.globalAlpha = 1.0;
    });
}

// Initial draw
drawField();
</script>
